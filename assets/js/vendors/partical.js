!function(){const i=document.getElementById("starCanvas"),h=i.getContext("2d");function t(){var t=document.querySelector("section");i.width=t.clientWidth,i.height=t.clientHeight}window.addEventListener("resize",t),t();let e=[];class a{constructor(){this.x=Math.random()*i.width,this.y=Math.random()*i.height,this.radius=2*Math.random(),this.dy=.5*Math.random()+.2}draw(){h.beginPath(),h.arc(this.x,this.y,this.radius,0,2*Math.PI),h.fillStyle="white",h.fill()}update(){this.y+=this.dy,this.y>i.height&&(this.y=0,this.x=Math.random()*i.width),this.draw()}}e=[];for(let t=0;t<70;t++)e.push(new a);!function t(){h.clearRect(0,0,i.width,i.height),e.forEach(t=>t.update()),requestAnimationFrame(t)}()}();